num=1
for num in range(1,101):
    if num%3==0 and num%5!=0 :
        print(str(num)+" : Fizz")
        continue
    elif num%5==0 and num%3!=0:
        print(str(num)+" : Buzz")
        continue
    elif num%3==0 and num%5==0:
        print(str(num)+" : FizzBuzz")
        continue
    

sentence=input("enter the sentence: ")
for i in range(len(sentence)-1,-1,-1):
    print(sentence[i])    
  

The_List=[1,2,3,4,5]
while True:
    num=input("enter the number and enter q to quit: ")
    if num=="q":
        break
    The_List.append(int(num))
sum=0
for number in The_List:
    sum=sum+number
print(sum)

while True:
    Username=input("enter your username: ")
    Password=input("enter your password: ")
    if Username=="admin" and Password=="menofia2025":
        print("login successful")
        break
    else:
        print("Invalid username or password, try again.")
    

sentence=input("enter the sentence: ")
print(len(sentence))
print(sentence.title())
print(sentence.upper())
print(sentence.lower())


Programming_lang={
   "Python":"December 1989",
   "Java":"May 1995",
   "C++":"February 1985",
   "C#":"February 2002",
   "JavaScript":"December 1995"
}
for name,date in Programming_lang.items():
    print(f"the programming language {name} was created in {date}")

def search(name):
    for i in range(len(Students)):
        if Students[i]==name:
            print(f"the student {name} is found in index {i}")
            return 
    print(f"the student {name} is not found")
    return 

Students=["Mohanad","Yassen","Zakaryia","Mohmoud"]
search("Mohanad")

    

     
  

def Product(nums):
    product=1
    for num in nums:
        product*=num
    return product
Prime_nums=(2,3,5,7,11)
print(Product(Prime_nums))

sentence=input("enter the sentence with comma-separated words: ")
words=sentence.split(",")

print(words)
for word in words:
    print(word)

Products={
    "laptop":1200,
    "mouse":100,
    "keyboard":150,
    "monitor":200
}
checked_product=input("enter the product name: ")
if checked_product in Products:
    print(f"the price of {checked_product} is {Products[checked_product]}")
else:
    print(f"the product {checked_product} is not found")

def remove_duplicates(nums):
    return list(set(nums))
nums=[1,2,3,4,5,6,7,8,9,10,1,2,3,4,5,6,7,8,9,10]
print(remove_duplicates(nums))

def remove_duplicates(nums):
    unique_nums=[]
    for num in nums:
        if num not in unique_nums:
            unique_nums.append(num)
    return unique_nums

nums=[1,2,3,4,5,6,7,8,9,10,1,2,3,4,5,6,7,8,9,10]
print(remove_duplicates(nums))

Intended_List=[num for num in range(1,51) if num%7==0]
print(Intended_List)

# Fixed version with proper error handling and retry logic
while True:
    try:
        student_id = int(input("Enter your student ID: "))
        print(f"Valid student ID entered: {student_id}")
        break  # Exit the loop when valid input is received
    except ValueError:
        print("Invalid student ID. Please enter a valid number.")
    
        
    


def frequency_count(file):
    with open(file, "r") as f:
        content = f.read().split()
    frequency = {}      
    for word in content:
        if word in frequency.keys():
            frequency[word] += 1
        else:
            frequency[word]=1 
    return (frequency)

result=frequency_count('Repeated_Word_Count.txt')
print(result)


def sort_roster_by_score(roster):
    sorted_roster = []
    for student in roster:
        inserted = False
        for i in range(len(sorted_roster)):
            if student[1] >= sorted_roster[i][1]:
                sorted_roster.insert(i, student)  
                inserted = True
                break
        if not inserted:  # add lowest score student
            sorted_roster.append(student)
    return sorted_roster

  
student_roster = [
    ["Mohanad", 85],
    ["Yassen", 92],
    ["Zakaryia", 78],
    ["Mohmoud", 88]
]

sorted_roster = sort_roster_by_score(student_roster)
print(sorted_roster)

def reverse(right_order):
    reversed_order = {}
    for key, value in right_order.items():
        if value in reversed_order.values():
            continue
        reversed_order[value] = key
    return reversed_order

right_order = {1:"Mohanad", 2:"Yassen", 3:"Zakaryia", 4:"Mohmoud", 5:"Yassen", 6:"Omar"}
reversed_order = reverse(right_order)
print(reversed_order)
